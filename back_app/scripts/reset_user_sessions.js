const mongoose = require('mongoose');
const User = require('../src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

// New session interval (6 hours in milliseconds)
const NEW_SESSION_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours

async function resetUserSessions() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find the specific user
    console.log('🔍 <NAME_EMAIL>...');
    const user = await User.findOne({ email: '<EMAIL>' });

    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log(`👤 Found user: ${user.email}`);
    
    // Completely reset sessions to initial state
    console.log('🔄 Resetting sessions...');
    
    user.sessions = [
      {
        sessionNumber: 1,
        isLocked: false,
        isClaimed: false,
        completedAt: null,
        nextUnlockAt: null,
        unlockedAt: new Date()
      },
      {
        sessionNumber: 2,
        isLocked: true,
        isClaimed: false,
        completedAt: null,
        nextUnlockAt: null,
        unlockedAt: null
      },
      {
        sessionNumber: 3,
        isLocked: true,
        isClaimed: false,
        completedAt: null,
        nextUnlockAt: null,
        unlockedAt: null
      },
      {
        sessionNumber: 4,
        isLocked: true,
        isClaimed: false,
        completedAt: null,
        nextUnlockAt: null,
        unlockedAt: null
      }
    ];

    // Save the user
    await user.save();
    console.log('✅ Sessions reset successfully');

    // Now complete Session 1 and set up Session 2 with 6-hour timer
    console.log('🎯 Setting up test scenario...');
    
    // Mark Session 1 as completed
    user.sessions[0].completedAt = new Date();
    user.sessions[0].isClaimed = true;
    
    // Set Session 2 to unlock after 6 hours
    const now = new Date();
    user.sessions[1].isLocked = true;
    user.sessions[1].nextUnlockAt = new Date(now.getTime() + NEW_SESSION_INTERVAL);
    
    await user.save();
    
    console.log('✅ Test scenario set up:');
    console.log('  - Session 1: Completed');
    console.log('  - Session 2: Locked for 6 hours');
    console.log('  - Session 3: Locked');
    console.log('  - Session 4: Locked');
    
    const remainingHours = NEW_SESSION_INTERVAL / (1000 * 60 * 60);
    console.log(`⏰ Session 2 will unlock in ${remainingHours} hours`);

  } catch (error) {
    console.error('❌ Error resetting sessions:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the reset
resetUserSessions();
