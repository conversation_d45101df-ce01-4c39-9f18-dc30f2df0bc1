const mongoose = require('mongoose');
const User = require('../src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

async function checkSessions() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all users
    console.log('🔍 Finding all users...');
    const users = await User.find({}).limit(5); // Limit to first 5 users for debugging

    console.log(`📊 Found ${users.length} users`);

    for (const user of users) {
      console.log(`\n👤 User: ${user.email || user.firebaseUid || user.inviteCode}`);
      console.log(`📅 Sessions (${user.sessions.length}):`);
      
      for (const session of user.sessions) {
        const now = new Date();
        const nextUnlockAt = session.nextUnlockAt ? new Date(session.nextUnlockAt) : null;
        const remainingMs = nextUnlockAt ? nextUnlockAt.getTime() - now.getTime() : 0;
        const remainingSeconds = Math.round(remainingMs / 1000);
        
        console.log(`  Session ${session.sessionNumber}:`);
        console.log(`    - isLocked: ${session.isLocked}`);
        console.log(`    - isClaimed: ${session.isClaimed}`);
        console.log(`    - completedAt: ${session.completedAt}`);
        console.log(`    - nextUnlockAt: ${session.nextUnlockAt}`);
        console.log(`    - remaining: ${remainingSeconds}s`);
      }
    }

    // Also check if there are any users with sessions at all
    const totalUsers = await User.countDocuments({});
    const usersWithSessions = await User.countDocuments({ 'sessions.0': { $exists: true } });
    
    console.log(`\n📊 Database Summary:`);
    console.log(`  - Total users: ${totalUsers}`);
    console.log(`  - Users with sessions: ${usersWithSessions}`);

  } catch (error) {
    console.error('❌ Error checking sessions:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the check
checkSessions();
