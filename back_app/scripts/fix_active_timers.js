const mongoose = require('mongoose');
const User = require('../src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

// New session interval (6 hours in milliseconds)
const NEW_SESSION_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours

async function fixActiveTimers() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all users
    console.log('🔍 Finding users with active timers...');
    const users = await User.find({});

    let updatedUsers = 0;
    let updatedSessions = 0;

    for (const user of users) {
      let userUpdated = false;
      
      for (const session of user.sessions) {
        if (session.nextUnlockAt) {
          const now = new Date();
          const nextUnlockAt = new Date(session.nextUnlockAt);
          const remainingMs = nextUnlockAt.getTime() - now.getTime();
          
          // If there's an active timer (future unlock time)
          if (remainingMs > 0) {
            console.log(`\n👤 User: ${user.email || user.firebaseUid}`);
            console.log(`📅 Session ${session.sessionNumber}: ${Math.round(remainingMs / 1000)}s remaining`);
            
            // Update to 6 hours from now
            session.nextUnlockAt = new Date(now.getTime() + NEW_SESSION_INTERVAL);
            userUpdated = true;
            updatedSessions++;
            
            console.log(`✅ Updated Session ${session.sessionNumber} to unlock in 6 hours`);
          }
        }
      }
      
      if (userUpdated) {
        await user.save();
        updatedUsers++;
        console.log(`💾 Saved user updates`);
      }
    }

    console.log('\n🎉 Update completed!');
    console.log(`📊 Summary:`);
    console.log(`  - Users updated: ${updatedUsers}`);
    console.log(`  - Sessions updated: ${updatedSessions}`);
    console.log(`  - New timer duration: 6 hours`);

    // Also reset any sessions that might be in a bad state
    console.log('\n🔄 Resetting sessions for testing user...');
    const testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (testUser) {
      console.log(`👤 Found test user: ${testUser.email}`);
      
      // Reset all sessions to initial state with proper 6-hour timers
      testUser.sessions = [
        {
          sessionNumber: 1,
          isLocked: false,
          isClaimed: false,
          completedAt: null,
          nextUnlockAt: null,
          unlockedAt: new Date()
        },
        {
          sessionNumber: 2,
          isLocked: true,
          isClaimed: false,
          completedAt: null,
          nextUnlockAt: null,
          unlockedAt: null
        },
        {
          sessionNumber: 3,
          isLocked: true,
          isClaimed: false,
          completedAt: null,
          nextUnlockAt: null,
          unlockedAt: null
        },
        {
          sessionNumber: 4,
          isLocked: true,
          isClaimed: false,
          completedAt: null,
          nextUnlockAt: null,
          unlockedAt: null
        }
      ];
      
      await testUser.save();
      console.log('✅ Reset test user sessions to initial state');
    }

  } catch (error) {
    console.error('❌ Error fixing active timers:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the fix
fixActiveTimers();
