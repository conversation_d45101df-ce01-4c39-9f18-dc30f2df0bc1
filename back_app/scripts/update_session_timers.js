const mongoose = require('mongoose');
const User = require('../src/models/User');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://mstorsulam786:<EMAIL>/zero_koin';

// New session interval (6 hours in milliseconds)
const NEW_SESSION_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours

async function updateSessionTimers() {
  try {
    // Connect to database
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all users with active session timers
    console.log('🔍 Finding users with active session timers...');
    const users = await User.find({
      'sessions': {
        $elemMatch: {
          isLocked: true,
          nextUnlockAt: { $exists: true, $ne: null }
        }
      }
    });

    console.log(`📊 Found ${users.length} users with active session timers`);

    if (users.length === 0) {
      console.log('✅ No users with active session timers found');
      return;
    }

    let updatedUsers = 0;
    let updatedSessions = 0;

    // Update each user's session timers
    for (const user of users) {
      let userUpdated = false;
      
      console.log(`\n👤 Processing user: ${user.email || user.firebaseUid || user.inviteCode}`);
      
      for (const session of user.sessions) {
        if (session.isLocked && session.nextUnlockAt) {
          const now = new Date();
          const currentUnlockTime = new Date(session.nextUnlockAt);
          
          // Check if this session timer is still active (in the future)
          if (currentUnlockTime > now) {
            // Calculate how much time was remaining with old timer
            const remainingMs = currentUnlockTime.getTime() - now.getTime();
            
            console.log(`  📅 Session ${session.sessionNumber}: ${Math.round(remainingMs / 1000)}s remaining`);
            
            // Set new unlock time to 6 hours from now
            session.nextUnlockAt = new Date(now.getTime() + NEW_SESSION_INTERVAL);
            userUpdated = true;
            updatedSessions++;
            
            console.log(`  ✅ Updated Session ${session.sessionNumber} to unlock in 6 hours`);
          }
        }
      }
      
      if (userUpdated) {
        await user.save();
        updatedUsers++;
        console.log(`  💾 Saved user updates`);
      }
    }

    console.log('\n🎉 Update completed!');
    console.log(`📊 Summary:`);
    console.log(`  - Users updated: ${updatedUsers}`);
    console.log(`  - Sessions updated: ${updatedSessions}`);
    console.log(`  - New timer duration: 6 hours`);

  } catch (error) {
    console.error('❌ Error updating session timers:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the update
updateSessionTimers();
